import 'package:flutter/material.dart';
import '../widgets/themed_row.dart'; // Import our new widget

class HomeScreen extends StatelessWidget {
   HomeScreen({super.key});

  // --- Mock Data (to be replaced by API calls) ---
// --- NEW MOCK DATA STRUCTURE ---
final Map<String, List<Map<String, String>>> allData = {
  'Trending in Accra': const [
    {'type': 'image', 'title': 'Osu Night Market', 'subtitle': 'Street Food', 'imageUrl': 'https://picsum.photos/seed/osu/400/300', 'mediaUrl': 'https://picsum.photos/seed/osu/800/1200'},
    {'type': 'video', 'title': 'Sandbox Beach Club', 'subtitle': 'Beach Bar', 'imageUrl': 'https://picsum.photos/seed/sandbox/400/300', 'mediaUrl': 'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4'},
    {'type': 'image', 'title': 'The Shop Accra', 'subtitle': 'Cafe & Lifestyle', 'imageUrl': 'https://picsum.photos/seed/shop/400/300', 'mediaUrl': 'https://picsum.photos/seed/shop/800/1200'},
    {'type': 'image', 'title': 'Jamestown Lighthouse', 'subtitle': 'Historic Landmark', 'imageUrl': 'https://picsum.photos/seed/jamestown/400/300', 'mediaUrl': 'https://picsum.photos/seed/jamestown/800/1200'},
  ],
  'Popular Playlists': const [
    {'type': 'voice_note', 'title': 'Accra\'s Best Kelewele', 'subtitle': 'By @gaudking', 'imageUrl': 'https://picsum.photos/seed/kelewele/400/300', 'mediaUrl': 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3'},
    {'type': 'image', 'title': 'Sunday Brunch Spots', 'subtitle': 'By @chapper', 'imageUrl': 'https://picsum.photos/seed/brunch/400/300', 'mediaUrl': 'https://picsum.photos/seed/brunch/800/1200'},
    {'type': 'video', 'title': 'Hidden Art Galleries', 'subtitle': 'By @wicker', 'imageUrl': 'https://picsum.photos/seed/art/400/300', 'mediaUrl': 'https://storage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'},
  ],
  'Community Highlights': const [
    {'type': 'voice_note', 'title': 'Kpakpo Shito recommendations?', 'subtitle': '8 comments', 'imageUrl': 'https://picsum.photos/seed/shito/400/300', 'mediaUrl': 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3'},
    {'type': 'image', 'title': 'Best tailor for Kente?', 'subtitle': '15 comments', 'imageUrl': 'https://picsum.photos/seed/kente/400/300', 'mediaUrl': 'https://picsum.photos/seed/kente/800/1200'},
  ],
};
@override
Widget build(BuildContext context) {
  final categories = allData.keys.toList();
  return Scaffold(
    body: SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          children: [
            const SizedBox(height: 20),
            // Use ListView.builder to create the rows dynamically
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: categories.length,
              itemBuilder: (context, categoryIndex) {
                final categoryTitle = categories[categoryIndex];
                final items = allData[categoryTitle]!;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24.0),
                  child: ThemedRow(
                    categoryTitle: categoryTitle,
                    items: items,
                    // Pass all data and the current category index for navigation
                    onItemTap: (itemIndex) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DetailScrollViewer(
                            allCategoriesData: allData,
                            initialCategoryIndex: categoryIndex,
                            initialItemIndex: itemIndex,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            )
          ],
        ),
      ),
    ),
  );
}

}
