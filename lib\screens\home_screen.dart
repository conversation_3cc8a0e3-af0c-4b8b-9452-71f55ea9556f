import 'package:flutter/material.dart';
import '../widgets/themed_row.dart'; // Import our new widget

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  // --- Mock Data (to be replaced by API calls) ---
  final List<Map<String, String>> trendingPlaces = const [
    {
      'title': 'Osu Night Market',
      'subtitle': 'Street Food',
      'imageUrl': 'https://picsum.photos/seed/osu/400/300',
    },
    {
      'title': 'Sandbox Beach Club',
      'subtitle': 'Beach Bar',
      'imageUrl': 'https://picsum.photos/seed/sandbox/400/300',
    },
    {
      'title': 'The Shop Accra',
      'subtitle': 'Cafe & Lifestyle',
      'imageUrl': 'https://picsum.photos/seed/shop/400/300',
    },
    {
      'title': 'Jamestown Lighthouse',
      'subtitle': 'Historic Landmark',
      'imageUrl': 'https://picsum.photos/seed/jamestown/400/300',
    },
    {
      'title': 'Bloom Bar',
      'subtitle': 'Nightlife',
      'imageUrl': 'https://picsum.photos/seed/bloom/400/300',
    },
  ];

  final List<Map<String, String>> popularPlaylists = const [
    {
      'title': 'Accra\'s Best Kelewele',
      'subtitle': 'By @gaudking',
      'imageUrl': 'https://picsum.photos/seed/kelewele/400/300',
    },
    {
      'title': 'Sunday Brunch Spots',
      'subtitle': 'By @chapper',
      'imageUrl': 'https://picsum.photos/seed/brunch/400/300',
    },
    {
      'title': 'Hidden Art Galleries',
      'subtitle': 'By @wicker',
      'imageUrl': 'https://picsum.photos/seed/art/400/300',
    },
  ];

  final List<Map<String, String>> communityHighlights = const [
    {
      'title': 'Kpakpo Shito recommendations?',
      'subtitle': '8 comments',
      'imageUrl': 'https://picsum.photos/seed/shito/400/300',
    },
    {
      'title': 'Best tailor for Kente?',
      'subtitle': '15 comments',
      'imageUrl': 'https://picsum.photos/seed/kente/400/300',
    },
    {
      'title': 'Reliable handyman needed',
      'subtitle': '5 comments',
      'imageUrl': 'https://picsum.photos/seed/handyman/400/300',
    },
    {
      'title': 'Weekend getaway ideas',
      'subtitle': '12 comments',
      'imageUrl': 'https://picsum.photos/seed/getaway/400/300',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // The body is a SingleChildScrollView to allow the whole page to scroll
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Column(
            children: [
              // We can add a search bar or header here later
              const SizedBox(height: 20),

              // Here we build the vertical list of themed rows
              ThemedRow(title: 'Trending in Accra', items: trendingPlaces),
              const SizedBox(height: 24),
              ThemedRow(title: 'Popular Playlists', items: popularPlaylists),
              const SizedBox(height: 24),
              ThemedRow(
                title: 'Community Highlights',
                items: communityHighlights,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
