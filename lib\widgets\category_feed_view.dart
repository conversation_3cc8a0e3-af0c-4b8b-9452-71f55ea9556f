import 'package:flutter/material.dart';
import 'image_post_view.dart';
import 'video_post_view.dart';
import 'voice_note_view.dart';

class CategoryFeedView extends StatefulWidget {
  final int initialIndex;
  final List<Map<String, String>> items;

  const CategoryFeedView({
    super.key,
    required this.items,
    required this.initialIndex,
  });

  @override
  _CategoryFeedViewState createState() => _CategoryFeedViewState();
}

class _CategoryFeedViewState extends State<CategoryFeedView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        final item = widget.items[index];
        final type = item['type'];

        // Use a switch to determine which media widget to display
        switch (type) {
          case 'image':
            return ImagePostView(imageUrl: item['mediaUrl']!);
          case 'video':
            return VideoPostView(videoUrl: item['mediaUrl']!);
          case 'voice_note':
            return VoiceNoteView(
              audioUrl: item['mediaUrl']!,
              title: item['title']!,
            );
          default:
            return Container(
              color: Colors.grey,
              child: Center(child: Text('Unknown format: $type')),
            );
        }
      },
    );
  }
}