import 'package:flutter/material.dart';
import 'item_card.dart'; // Import the card widget

class ThemedRow extends StatelessWidget {
  final String title;
  // This will hold the data for the cards, e.g., a list of places or playlists
  final List<Map<String, String>> items;

  const ThemedRow({super.key, required this.title, required this.items});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row Title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 12),
        // Horizontal List of Cards
        SizedBox(
          height: 200, // Fixed height for the horizontal list
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return ItemCard(
                // Use placeholder images for now
                imageUrl:
                    item['imageUrl'] ?? 'https://via.placeholder.com/160x120',
                title: item['title'] ?? 'No Title',
                subtitle: item['subtitle'] ?? '',
              );
            },
          ),
        ),
      ],
    );
  }
}
